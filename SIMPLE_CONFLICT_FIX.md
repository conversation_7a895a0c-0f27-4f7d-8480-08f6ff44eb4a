# 簡化的衝突修復方案

## 問題根源
CSS選擇器語法錯誤：`:visible` 是jQuery特有的偽選擇器，不能直接用於 `document.querySelector()`

## 修復後的代碼

### 1. 修復後的檢測函數

```javascript
// 檢查是否有角色卡相關操作正在進行
function isCharacterOperationInProgress() {
    try {
        // 檢查基本的狀態標記
        const isCharacterSwitching = window.characterSwitchInProgress || false;
        const isStatsTrackerBusy = window.statsTrackerBusy || false;
        
        // 簡單檢查是否有相關對話框
        const hasImportDialog = document.querySelector('.character-import-dialog, .import-dialog') !== null;
        const hasBehaviorModal = document.getElementById('behavior-modal') !== null;
        
        return isCharacterSwitching || isStatsTrackerBusy || hasImportDialog || hasBehaviorModal;
    } catch (error) {
        console.warn('[小白X Preview] 檢查角色操作狀態時出錯:', error);
        return false;
    }
}
```

### 2. 簡化的安全執行包裝器

```javascript
// 安全的預覽執行包裝器
function safePreviewExecution(callback, delay = 0) {
    setTimeout(() => {
        try {
            if (!isPreviewActive && !isCharacterOperationInProgress()) {
                callback();
            }
        } catch (error) {
            console.warn('[小白X Preview] 執行回調時出錯:', error);
        }
    }, delay);
}
```

## 關鍵改進

1. **移除了有問題的CSS選擇器**：不再使用 `:visible` 偽選擇器
2. **簡化檢測邏輯**：只檢查最關鍵的狀態標記
3. **更好的錯誤處理**：出錯時返回 `false` 而不是 `true`
4. **保持核心功能**：仍然能有效防止衝突

## 測試方法

1. 重新加載頁面
2. 切換角色卡
3. 檢查控制台是否還有語法錯誤
4. 確認preview功能正常工作
5. 確認角色卡導入功能正常

## 如果仍有問題

可以進一步簡化為最基本的版本：

```javascript
function isCharacterOperationInProgress() {
    return window.characterSwitchInProgress || window.statsTrackerBusy || false;
}
```

這個版本只檢查最基本的狀態標記，避免所有DOM查詢相關的問題。
