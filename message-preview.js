import { extension_settings, getContext } from "../../../extensions.js";
import { saveSettingsDebounced, eventSource, event_types, main_api } from "../../../../script.js";
import { callGenericPopup, POPUP_TYPE } from "../../../popup.js";
import { prepareOpenAIMessages } from "../../../openai.js";

const EXT_ID = "LittleWhiteBox";
const PREVIEW_MODULE_NAME = "xiaobaix-preview";

let apiRequestHistory = [];
let lastApiRequest = null;
const MAX_HISTORY_RECORDS = 50;

// 創建作用域攔截器
const fetchInterceptor = createScopedFetchInterceptor();

// 獲取設置
function getSettings() {
    if (!extension_settings[EXT_ID].preview) {
        extension_settings[EXT_ID].preview = {
            enabled: true,
            maxPreviewLength: 300
        };
    }
    return extension_settings[EXT_ID].preview;
}

// 創建預覽按鈕
function createPreviewButton() {
    return $(`<div id="message_preview_btn" class="fa-solid fa-coffee interactable" title="預覽將要發送給LLM的消息"></div>`).on('click', showMessagePreview);
}

// 創建歷史按鈕
function createMessageHistoryButton() {
    return $(`<div title="查看此消息前的歷史記錄" class="mes_button mes_history_preview fa-solid fa-coffee"></div>`);
}

// 為消息添加歷史按鈕
function addHistoryButtonsToMessages() {
    const settings = getSettings();
    if (!settings.enabled) return;

    $('.mes_history_preview').remove();
    $('#chat .mes').each(function() {
        const mesId = parseInt($(this).attr('mesid'));
        if (mesId <= 0) return;

        const extraButtons = $(this).find('.extraMesButtons');
        if (extraButtons.length > 0) {
            const historyButton = createMessageHistoryButton().on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                showMessageHistoryPreview(mesId);
            });
            extraButtons.prepend(historyButton);
        }
    });
}

// 查找對應的API請求數據
function findApiRequestForMessage(messageId) {
    if (apiRequestHistory.length === 0) return null;
    
    const strategies = [
        record => record.associatedMessageId === messageId,
        record => record.messageId === messageId,
        record => record.messageId === messageId - 1,
        record => record.chatLength === messageId
    ];

    for (const strategy of strategies) {
        const match = apiRequestHistory.find(strategy);
        if (match) return match;
    }

    const candidates = apiRequestHistory.filter(record => record.messageId <= messageId);
    return candidates.length > 0 ? candidates.sort((a, b) => b.messageId - a.messageId)[0] : null;
}

// 顯示消息歷史預覽
async function showMessageHistoryPreview(messageId) {
    try {
        const settings = getSettings();
        if (!settings.enabled) return;

        const context = getContext();
        const historyMessages = context.chat.slice(0, messageId);
        if (historyMessages.length === 0) {
            toastr.info('此消息之前沒有歷史記錄');
            return;
        }

        const apiRecord = findApiRequestForMessage(messageId);
        let messageData;

        if (apiRecord?.messages?.length) {
            messageData = {
                type: 'api_intercepted',
                messages: apiRecord.messages,
                model: apiRecord.model,
                timestamp: apiRecord.timestamp,
                userInput: extractActualUserInput(apiRecord.messages)
            };
        } else {
            const originalChat = [...context.chat];
            context.chat = historyMessages;
            messageData = await getCurrentMessageContent();
            context.chat = originalChat;
        }

        messageData.isHistoryPreview = true;
        messageData.targetMessageId = messageId;
        messageData.historyCount = historyMessages.length;

        const popupContent = `<div class="message-preview-container"><div class="message-preview-content-box">${formatPreviewContent(messageData, true)}</div></div>`;
        await callGenericPopup(popupContent, POPUP_TYPE.TEXT, `消息歷史預覽 - 第 ${messageId + 1} 條消息之前`, { wide: true, large: true });

    } catch (error) {
        toastr.error('無法顯示消息歷史預覽: ' + error.message);
    }
}

// 獲取當前消息內容
async function getCurrentMessageContent() {
    const context = getContext();
    const textareaText = String($('#send_textarea').val());
    const character = context.characters[context.characterId];
    
    if (!character) throw new Error('沒有選擇角色');

    if (lastApiRequest && (Date.now() - lastApiRequest.timestamp < 60000)) {
        return {
            type: 'api_intercepted',
            messages: lastApiRequest.messages,
            userInput: textareaText,
            model: lastApiRequest.model,
            timestamp: lastApiRequest.timestamp
        };
    }

    if (main_api === 'openai') {
        try {
            const { getWorldInfoPrompt } = await import('../../../world-info.js');
            const { power_user } = await import('../../../power-user.js');
            const { getExtensionPrompt, extension_prompt_types } = await import('../../../extensions.js');

            const fullChat = [...context.chat];
            if (textareaText.trim()) {
                fullChat.push({
                    name: context.name1 || 'User',
                    is_user: true,
                    send_date: Date.now(),
                    mes: textareaText
                });
            }

            const worldInfo = await getWorldInfoPrompt(fullChat, context.maxContext || 4096).catch(() => ({ worldInfoBefore: '', worldInfoAfter: '' }));
            
            const extensionPrompts = [];
            for (const type of [extension_prompt_types.BEFORE_PROMPT, extension_prompt_types.IN_PROMPT, extension_prompt_types.AFTER_PROMPT]) {
                const prompt = await getExtensionPrompt(type).catch(() => null);
                if (prompt) extensionPrompts.push({ role: 'system', content: prompt });
            }

            const [messages, counts] = await prepareOpenAIMessages({
                name2: character.name,
                charDescription: character.description || '',
                charPersonality: character.personality || '',
                scenario: character.scenario || '',
                worldInfoBefore: worldInfo.worldInfoBefore || '',
                worldInfoAfter: worldInfo.worldInfoAfter || '',
                extensionPrompts,
                personaDescription: power_user?.persona_description || '',
                messages: fullChat,
                messageExamples: character.mes_example ? [character.mes_example] : [],
            }, false);

            return { type: 'openai', messages, tokenCount: counts, userInput: textareaText };
        } catch (error) {
            throw error;
        }
    }

    return { type: 'other', userInput: textareaText, character, chat: context.chat, api: main_api };
}

// 改進的數據捕獲方式
async function captureRealMessageData() {
    return new Promise((resolve) => {
        const textareaText = String($('#send_textarea').val()).trim();
        if (!textareaText) {
            resolve({ success: false, error: '請先在輸入框中輸入內容' });
            return;
        }

        const context = getContext();
        const originalChat = [...context.chat];
        
        // 臨時消息
        const tempMessage = {
            name: context.name1 || 'User',
            is_user: true,
            send_date: Date.now(),
            mes: textareaText,
            extra: { isPreviewTemp: true }
        };
        context.chat.push(tempMessage);

        // 激活攔截器
        fetchInterceptor.reset();
        fetchInterceptor.activate();

        // 阻止實際的消息創建
        const restoreMessageCreation = preventMessageCreation();
        
        const cleanup = () => {
            fetchInterceptor.deactivate();
            restoreMessageCreation();
            context.chat.length = originalChat.length;
            context.chat.splice(0, context.chat.length, ...originalChat);
        };

        // 設置超時
        const timeout = setTimeout(() => {
            const capturedData = fetchInterceptor.getCapturedData();
            cleanup();
            resolve({ 
                success: !!capturedData, 
                data: capturedData, 
                userInput: textareaText 
            });
        }, 3000);

        try {
            // 觸發發送
            $('#send_but').click();
            
            // 短暫延遲後檢查結果
            setTimeout(() => {
                clearTimeout(timeout);
                const capturedData = fetchInterceptor.getCapturedData();
                cleanup();
                resolve({ 
                    success: !!capturedData, 
                    data: capturedData, 
                    userInput: textareaText 
                });
            }, 1000);
        } catch (error) {
            clearTimeout(timeout);
            cleanup();
            resolve({ success: false, error: error.message, userInput: textareaText });
        }
    });
}

// 阻止消息創建
function preventMessageCreation() {
    const context = getContext();
    const originalMethods = {
        push: Array.prototype.push,
        unshift: Array.prototype.unshift,
        splice: Array.prototype.splice,
        appendChild: Element.prototype.appendChild,
        insertBefore: Element.prototype.insertBefore
    };
    
    const originalChatMethods = {
        push: context.chat.push,
        unshift: context.chat.unshift,
        splice: context.chat.splice
    };
    
    context.chat.push = context.chat.unshift = () => context.chat.length;
    context.chat.splice = (start, deleteCount, ...items) => {
        return items.length > 0 ? [] : originalChatMethods.splice.call(context.chat, start, deleteCount);
    };
    
    const originalFunctions = {};
    ['Generate', 'addOneMessage', 'printMessages'].forEach(fn => {
        if (window[fn]) {
            originalFunctions[fn] = window[fn];
            window[fn] = fn === 'Generate' ? () => Promise.resolve() : () => {};
        }
    });
    
    const isMessageElement = (child) => child?.classList && (
        child.classList.contains('mes') || 
        child.classList.contains('message') || 
        child.id?.includes('mes')
    );
    
    Element.prototype.appendChild = function(child) { 
        return isMessageElement(child) ? child : originalMethods.appendChild.call(this, child); 
    };
    
    Element.prototype.insertBefore = function(newNode, referenceNode) { 
        return isMessageElement(newNode) ? newNode : originalMethods.insertBefore.call(this, newNode, referenceNode); 
    };
    
    return function restoreMessageCreation() {
        context.chat.push = originalChatMethods.push;
        context.chat.unshift = originalChatMethods.unshift;
        context.chat.splice = originalChatMethods.splice;
        
        Object.keys(originalFunctions).forEach(fn => {
            window[fn] = originalFunctions[fn];
        });
        
        Element.prototype.appendChild = originalMethods.appendChild;
        Element.prototype.insertBefore = originalMethods.insertBefore;
    };
}

// 顯示消息預覽
async function showMessagePreview() {
    try {
        const settings = getSettings();
        if (!settings.enabled) return;

        toastr.info('正在捕獲消息數據...');
        const captureResult = await captureRealMessageData();
        let rawContent = '';

        if (captureResult.success && captureResult.data?.messages) {
            const { data } = captureResult;
            rawContent = `=== 捕獲到的LLM API請求 ===\nURL: ${data.url}\nModel: ${data.model || 'Unknown'}\nMessages Count: ${data.messages.length}\n`;
            if (captureResult.userInput) rawContent += `📝 用戶輸入: "${captureResult.userInput}"\n`;
            rawContent += `\n${formatMessagesArray(data.messages, captureResult.userInput)}`;
        } else {
            const messageData = await getCurrentMessageContent();
            rawContent = formatPreviewContent(messageData, false);
        }

        const popupContent = `<div class="message-preview-container"><div class="message-preview-content-box">${rawContent}</div></div>`;
        await callGenericPopup(popupContent, POPUP_TYPE.TEXT, '消息預覽 - 數據捕獲', { wide: true, large: true });

    } catch (error) {
        toastr.error('無法顯示消息預覽: ' + error.message);
    }
}

// 格式化消息數組
function formatMessagesArray(messages, userInput) {
    let content = `messages: [\n`;
    let processedMessages = [...messages];
    
    if (processedMessages.length >= 2) {
        const [lastMsg, secondLastMsg] = processedMessages.slice(-2);
        if (lastMsg.role === 'user' && secondLastMsg.role === 'user' && 
            lastMsg.content === secondLastMsg.content && lastMsg.content === userInput) {
            processedMessages.pop();
        }
    }

    processedMessages.forEach((msg, index) => {
        const msgContent = msg.content || '';
        let inputNote = '';

        if (msg.role === 'user') {
            if (msgContent === userInput) {
                inputNote = ' // 👈 實際發送的內容';
            } else if (msgContent.startsWith('/')) {
                const commandTypes = ['echo', 'gen', 'send', 'sendas'].filter(cmd => msgContent.includes(`/${cmd}`));
                inputNote = ` // 👈 不會實際發送給ai的斜槓命令${commandTypes.length ? ` (${commandTypes.join(' + ').toUpperCase()})` : ''}`;
                if (userInput && userInput !== msgContent && !userInput.startsWith('[') && userInput.length < 100) {
                    const displayContent = userInput.length > 50 ? userInput.substring(0, 50) + '...' : userInput;
                    inputNote += ` (核心內容: "${displayContent}")`;
                }
            }
        }

        content += `  {\n    role: '${msg.role}'${inputNote},\n`;
        content += `    content: ${JSON.stringify(msgContent, null, 4).replace(/^/gm, '    ')}\n  }`;
        if (index < processedMessages.length - 1) content += ',';
        content += '\n';
    });
    content += `]`;
    return content;
}

// 格式化預覽內容
function formatPreviewContent(messageData, isHistory) {
    let content = '';

    if (isHistory) {
        content += `=== 📚 消息歷史預覽 ===\n目標消息: 第 ${messageData.targetMessageId + 1} 條\n`;
        content += `歷史記錄數量: ${messageData.historyCount} 條消息\n\n`;
    }

    if (messageData.type === 'api_intercepted' && messageData.messages) {
        content += `=== 捕獲到的LLM API請求${isHistory ? ' (歷史記錄)' : ''} ===\n`;
        content += `Model: ${messageData.model || 'Unknown'}\nMessages Count: ${messageData.messages.length}\n`;
        if (messageData.userInput) content += `📝 用戶輸入: "${messageData.userInput}"\n`;
        content += `\n${formatMessagesArray(messageData.messages, messageData.userInput)}`;
    } else if (messageData.type === 'openai' && messageData.messages) {
        content += '=== OpenAI 消息格式 ===\n';
        if (messageData.tokenCount) {
            const tokenCount = typeof messageData.tokenCount === 'object' ? 
                JSON.stringify(messageData.tokenCount) : messageData.tokenCount;
            content += `預估Token數量: ${tokenCount}\n`;
        }
        content += `消息總數: ${messageData.messages.length}\n\n${formatMessagesArray(messageData.messages, messageData.userInput)}`;
    } else if (messageData.chat?.length) {
        content += `=== 💬 聊天歷史記錄 ===\n聊天歷史總數: ${messageData.chat.length}\n\n`;
        messageData.chat.forEach((msg, index) => {
            const role = msg.is_user ? '用戶' : (msg.name || '角色');
            content += `[${index + 1}] ${role}: ${msg.mes || ''}\n`;
            if (msg.send_date) content += `時間: ${new Date(msg.send_date).toLocaleString()}\n\n`;
        });
    } else {
        content += '無法獲取消息內容\n';
    }
    return content;
}

// 提取實際的用戶輸入
function extractActualUserInput(messages) {
    if (!messages?.length) return '';
    for (let i = messages.length - 1; i >= 0; i--) {
        const message = messages[i];
        if (message.role === 'user') {
            const content = message.content || '';
            return content.startsWith('/') ? parseSlashCommandContent(content) : content;
        }
    }
    return '';
}

// 解析斜槓命令內容
function parseSlashCommandContent(content) {
    const commands = content.includes('\n') ? 
        content.split('\n').filter(line => line.trim().startsWith('/')) : 
        content.split('|').map(cmd => cmd.trim()).filter(cmd => cmd);

    let extractedContent = '';
    let commandTypes = [];

    for (const command of commands) {
        const patterns = [
            [/^\/gen\s+([\s\S]+)$/, 'gen'],
            [/^\/send\s+(.+)$/s, 'send'],
            [/^\/echo\s+(.+)$/s, 'echo']
        ];

        for (const [pattern, type] of patterns) {
            const match = command.match(pattern);
            if (match) {
                if (!extractedContent) extractedContent = match[1].trim();
                commandTypes.push(type);
                break;
            }
        }
    }

    if (extractedContent) {
        return extractedContent.length > 100 ? extractedContent.substring(0, 100) + '...' : extractedContent;
    }
    return commandTypes.length > 0 ? `[${commandTypes.join(' + ').toUpperCase()} 命令組合]` : 
        (content.length > 50 ? content.substring(0, 50) + '...' : content);
}

// 保存API請求到歷史
function saveApiRequestToHistory(requestData) {
    const context = getContext();
    const historyItem = {
        ...requestData,
        messageId: context.chat?.length || 0,
        chatLength: context.chat?.length || 0,
        characterName: context.characters?.[context.characterId]?.name || 'Unknown'
    };

    apiRequestHistory.unshift(historyItem);
    if (apiRequestHistory.length > MAX_HISTORY_RECORDS) {
        apiRequestHistory = apiRequestHistory.slice(0, MAX_HISTORY_RECORDS);
    }
}

// 創建作用域攔截器
function createScopedFetchInterceptor() {
    const originalFetch = window.fetch;
    let isActive = false;
    let capturedData = null;
    
    const interceptor = function(...args) {
        const [url, options] = args;
        
        if (isActive && isTargetLLMRequest(url, options)) {
            try {
                const requestData = JSON.parse(options.body);
                if (requestData.messages) {
                    capturedData = {
                        url, 
                        messages: requestData.messages, 
                        model: requestData.model,
                        timestamp: Date.now(), 
                        fullRequest: requestData
                    };
                }
            } catch (e) {
                console.warn('[小白X] 解析請求數據失敗:', e);
            }
            
            // 返回模擬響應以阻止實際請求
            return Promise.resolve(new Response(JSON.stringify({
                choices: [{ message: { content: "PREVIEW_MODE_INTERCEPTED" } }]
            }), { status: 200, headers: { 'Content-Type': 'application/json' } }));
        }
        
        return originalFetch.apply(this, args);
    };
    
    return {
        activate() {
            if (!isActive) {
                isActive = true;
                window.fetch = interceptor;
            }
        },
        deactivate() {
            if (isActive) {
                isActive = false;
                window.fetch = originalFetch;
            }
        },
        getCapturedData() {
            return capturedData;
        },
        reset() {
            capturedData = null;
        }
    };
}

// 精確判斷目標請求
function isTargetLLMRequest(url, options) {
    if (!url || !options?.body) return false;
    
    // 排除非LLM請求
    const excludedPatterns = [
        '/api/chats/',
        '/api/characters',
        '/api/settings',
        '/api/images',
        '/api/files',
        'blob:',
        'data:',
        'chrome-extension:'
    ];
    
    if (excludedPatterns.some(pattern => url.includes(pattern))) {
        return false;
    }
    
    // 檢查是否為LLM端點
    const llmEndpoints = [
        '/v1/chat/completions',
        '/api/openai',
        '/api/backends/chat-completions',
        '/generate',
        '/chat/completions',
        'claude',
        'anthropic'
    ];
    
    const isLLMEndpoint = llmEndpoints.some(endpoint => url.includes(endpoint));
    const hasMessages = options.body.includes('"messages"');
    
    return isLLMEndpoint && hasMessages;
}

// 設置事件監聽器
function setupEventListeners() {
    if (eventSource) {
        [event_types.CHARACTER_MESSAGE_RENDERED, event_types.USER_MESSAGE_RENDERED, 
         event_types.MESSAGE_RECEIVED, event_types.MESSAGE_SWIPED].forEach(eventType => {
            eventSource.on(eventType, () => setTimeout(addHistoryButtonsToMessages, 100));
        });
        
        eventSource.on(event_types.CHAT_CHANGED, () => {
            setTimeout(() => {
                addHistoryButtonsToMessages();
                apiRequestHistory = [];
            }, 200);
        });
        
        eventSource.on(event_types.MESSAGE_RECEIVED, (messageId) => {
            setTimeout(() => {
                const recentRequest = apiRequestHistory.find(record =>
                    !record.associatedMessageId && (Date.now() - record.timestamp) < 30000
                );
                if (recentRequest) recentRequest.associatedMessageId = messageId;
            }, 100);
        });
    }
}

// 模塊初始化
function initMessagePreview() {
    try {
        // 設置事件監聽器
        setupEventListeners();
        
        // 將預覽按鈕添加到發送按鈕前
        $("#send_but").before(createPreviewButton());
        
        // 初始化設置
        const settings = getSettings();
        $("#xiaobaix_preview_enabled").prop("checked", settings.enabled).on("change", function() {
            settings.enabled = $(this).prop("checked");
            saveSettingsDebounced();
            $('#message_preview_btn').toggle(settings.enabled);
            
            if (settings.enabled) {
                addHistoryButtonsToMessages();
            } else {
                $('.mes_history_preview').remove();
            }
        });
        
        if (!settings.enabled) $('#message_preview_btn').hide();
        
        // 添加歷史按鈕到現有消息
        addHistoryButtonsToMessages();
        
        console.log('[小白X] 消息預覽模塊初始化完成');
    } catch (error) {
        console.error('[小白X] 消息預覽模塊初始化失敗:', error);
    }
}

// 導出函數
export { initMessagePreview };
