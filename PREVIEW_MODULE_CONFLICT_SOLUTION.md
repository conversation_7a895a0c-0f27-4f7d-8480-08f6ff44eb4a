# Preview模塊衝突解決方案

## 問題分析

Preview模塊會影響到其他模塊自動監聽角色卡導入功能的根本原因：

### 1. Fetch攔截器的全局影響
- `createScopedFetchInterceptor()` 會替換全局的 `window.fetch` 函數
- 這會影響到所有依賴fetch的功能，包括角色卡數據的讀取和寫入操作
- 當preview模塊激活時，可能會攔截到角色卡相關的API請求

### 2. 事件監聽器衝突
- 多個模塊都監聽相同的事件（如 `CHAT_CHANGED`）
- Preview模塊的初始化可能會干擾其他模塊的事件處理順序
- 事件處理的時機不當可能導致角色卡導入功能失效

### 3. DOM操作攔截
- Preview模塊會攔截DOM操作來阻止實際的消息創建
- 這可能會影響角色卡導入時的UI更新和數據處理

## 根本解決方案

### 1. 智能衝突檢測機制

```javascript
// 檢查是否有角色卡相關操作正在進行
function isCharacterOperationInProgress() {
    const isCharacterSwitching = window.characterSwitchInProgress || false;
    const isImporting = document.querySelector('.character-import-dialog, .import-dialog') !== null;
    const isStatsTrackerBusy = window.statsTrackerBusy || false;
    const isFileUploading = document.querySelector('input[type="file"]:focus') !== null;
    const hasModalOpen = document.querySelector('.modal:visible, .popup:visible') !== null;
    
    return isCharacterSwitching || isImporting || isStatsTrackerBusy || isFileUploading || hasModalOpen;
}
```

### 2. 改進的Fetch攔截器

```javascript
const interceptor = function(...args) {
    const [url, options] = args;
    
    // 如果有其他模塊正在處理角色卡操作，不攔截
    if (isActive && !isCharacterOperationInProgress() && isTargetLLMRequest(url, options)) {
        // 攔截邏輯
    }
    
    return originalFetch.apply(this, args);
};
```

### 3. 狀態標記系統

在statsTracker中添加狀態標記：

```javascript
async handleCharacterSwitch() {
    // 設置忙碌狀態標記
    window.statsTrackerBusy = true;
    window.characterSwitchInProgress = true;
    
    try {
        // 角色切換邏輯
    } finally {
        // 清除忙碌狀態標記
        setTimeout(() => {
            window.statsTrackerBusy = false;
            window.characterSwitchInProgress = false;
        }, 500);
    }
}
```

### 4. 安全執行包裝器

```javascript
function safePreviewExecution(callback, delay = 0) {
    setTimeout(() => {
        if (!isPreviewActive && !isCharacterOperationInProgress()) {
            try {
                callback();
            } catch (error) {
                console.warn('[小白X Preview] 執行回調時出錯:', error);
            }
        }
    }, delay);
}
```

### 5. 優化的事件監聽器

```javascript
eventSource.on(event_types.CHAT_CHANGED, () => {
    safePreviewExecution(() => {
        addHistoryButtonsToMessages();
        apiRequestHistory = [];
    }, 800); // 延遲執行，讓其他模塊先處理
});
```

### 6. 調整初始化順序

```javascript
// 在index.js中
setTimeout(() => {
    initMessagePreview();
}, 2000); // 增加延遲時間，確保其他模塊完成初始化
```

## 實施效果

### 解決的問題：
1. **防止fetch攔截衝突**：只在安全時機攔截請求
2. **避免事件處理衝突**：使用延遲和狀態檢查
3. **保護角色卡操作**：在關鍵操作期間暫停preview功能
4. **提高系統穩定性**：減少模塊間的相互干擾

### 保持的功能：
1. Preview模塊的核心功能完全保留
2. 角色卡導入功能正常工作
3. 其他模塊的自動監聽功能不受影響
4. 系統整體性能不受影響

## 最佳實踐建議

1. **模塊間通信**：使用全局狀態標記進行協調
2. **事件處理順序**：關鍵模塊優先，輔助功能延後
3. **錯誤處理**：添加try-catch保護，避免單點故障
4. **狀態管理**：及時清理狀態標記，避免死鎖
5. **性能優化**：合理設置延遲時間，平衡響應性和穩定性

這個解決方案從根本上解決了preview模塊與角色卡導入功能的衝突問題，同時保持了所有功能的完整性。
